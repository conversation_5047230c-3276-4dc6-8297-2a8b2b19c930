<template>
  <div class="realty-game-results-page">
    <div class="max-ctr q-pa-xs rgrp">
      <!-- Loading State -->
      <div v-if="isLoading"
           class="loading-container text-center q-pa-xl">
        <q-spinner color="primary"
                   size="3em" />
        <div class="q-mt-md text-h6">Loading Results...</div>
        <div class="text-body2 text-grey-7">
          Crunching the numbers and comparing scores
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="error-container text-center q-pa-xl">
        <q-icon name="error"
                color="negative"
                size="3em" />
        <div class="q-mt-md text-h6 text-negative">Failed to Load Results</div>
        <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               @click="loadResults" />
      </div>

      <!-- Results Content -->
      <div v-else-if="results"
           class="results-content">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <div class="performance-badge q-mb-md">
            <q-icon :name="playerResults.performance_rating?.icon"
                    :color="playerResults.performance_rating?.color"
                    size="4em" />
            <div class="text-h4 text-weight-bold q-mt-md"
                 :class="`text-${playerResults.performance_rating?.color}`">
              {{ playerResults.performance_rating?.rating }}
            </div>
            <div class="text-h6 text-grey-7">
              {{ playerResults.total_score }} /
              {{ playerResults.max_possible_score }} points
            </div>
          </div>
          <h1 class="text-h4 text-weight-bold text-primary q-mb-lg">
            {{ isCurrentUserSession ? `Well done ${ssGameSession.game_player_nickname}, challenge complete` :
              `${ssGameSession.game_player_nickname}'s challenge results` }}
          </h1>

          <!-- Session Info and Overall Ranking -->
          <div class="session-info q-mb-md">
            <!-- <q-chip color="primary" text-color="white" icon="event">
              {{
                playerResults.session_date
                  ? new Date(playerResults.session_date).toLocaleString()
                  : ''
              }}
            </q-chip>
            <q-chip color="secondary" text-color="white" icon="person">
              {{ playerResults.session_guest_name }}
            </q-chip> -->
            <template v-if="overallRanking">
              <q-chip v-if="overallRanking.rank === 1"
                      color="amber-7"
                      text-color="black"
                      icon="military_tech"
                      size="lg"
                      class="q-mb-sm text-h6 text-weight-bold shadow-2"
                      style="font-size: 1.2em; padding: 0.7em 1.5em">
                🥇 {{ isCurrentUserSession ? 'Congratulations! You are the' : `${ssGameSession.game_player_nickname} is
                the` }}
                <span class="text-weight-bold">Top Player</span> (1st of
                {{ overallRanking.total_sessions }})
              </q-chip>
              <q-chip v-else-if="overallRanking.rank === 2"
                      color="blue-grey-2"
                      text-color="black"
                      icon="military_tech"
                      size="lg"
                      class="q-mb-sm text-h6 text-weight-bold shadow-2"
                      style="font-size: 1.2em; padding: 0.7em 1.5em">
                🥈 {{ isCurrentUserSession ? 'Amazing! You ranked' : `${ssGameSession.game_player_nickname} ranked` }}
                <span class="text-weight-bold">&nbsp;2nd&nbsp;</span> of
                {{ overallRanking.total_sessions }}
              </q-chip>
              <q-chip v-else
                      color="info"
                      text-color="white"
                      icon="emoji_events">
                {{ isCurrentUserSession ? 'You ranked' : `${ssGameSession.game_player_nickname} ranked` }} {{
                  overallRanking.rank }} of
                {{ overallRanking.total_sessions }}
              </q-chip>
            </template>
          </div>
          <div>
            <SocialSharing socialSharingPrompt="Sharing is caring: show your friends how you did in this property price game!"
                           socialSharingTitle="Check out how I did in this property price game"
                           urlProp=""></SocialSharing>
          </div>
          <p class="text-body1 text-grey-7">
            <!-- Here's how you performed on each property -->
          </p>
        </div>

        <!-- Leaderboard Section -->
        <q-card v-if="showLeaderboard && leaderboard.length > 0"
                class="leaderboard-card q-mb-lg"
                flat
                bordered>
          <q-card-section>
            <div class="text-h6 q-mb-md">
              <q-icon name="leaderboard"
                      color="primary"
                      size="sm"
                      class="q-mr-sm" />
              Leaderboard
            </div>
            <q-table :rows="leaderboard"
                     :columns="leaderboardColumns"
                     row-key="uuid"
                     flat
                     :pagination="{ rowsPerPage: 0 }"
                     :hide-bottom="leaderboard.length < 10"
                     class="leaderboard-table">
              <template v-slot:body-cell-performance_percentage="props">
                <q-td :props="props">
                  {{ props.row.performance_percentage }}%
                </q-td>
              </template>
              <template v-slot:body-cell-created_at="props">
                <q-td :props="props">
                  {{ new Date(props.row.created_at).toLocaleDateString() }}
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>

        <!-- Results Table -->
        <q-card class="results-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 q-mb-md">
              <q-icon name="leaderboard"
                      color="primary"
                      size="sm"
                      class="q-mr-sm" />
              {{ isCurrentUserSession ? 'How you performed on each property' : `How
              ${ssGameSession.game_player_nickname}
              performed on each property` }}
            </div>
            <!-- Note: :rows is now bound to gameBreakdown -->
            <q-table :rows="gameBreakdown"
                     :columns="resultsColumns"
                     row-key="uuid"
                     flat
                     :pagination="{ rowsPerPage: 0 }"
                     :hide-bottom="gameBreakdown.length < 10"
                     class="results-table">
              <!-- The v-slot templates remain largely the same because the `game_results` array structure is the same -->
              <template v-slot:body-cell-property="props">
                <q-td :props="props">
                  <div class="property-cell"
                       style="overflow: auto">
                    <router-link :to="{
                      name: 'rPriceGameProperty',
                      params: { propertyUuid: props.row.listing_uuid },
                    }">
                      <div class="text-weight-medium">
                        {{ props.row.estimate_title }}
                      </div>
                    </router-link>
                    <!-- <div class="text-weight-medium">{{ props.row.estimate_title }}</div> -->
                    <div class="text-caption text-grey-6">
                      {{ props.row.estimate_vicinity }}
                    </div>
                  </div>
                </q-td>
              </template>

              <template v-if="isCurrentUserSession"
                        v-slot:body-cell-guess="props">
                <q-td :props="props">
                  <!-- <div v-if="props.row.source_listing_currency"
                       class="text-weight-medium">
                    {{ formatPriceWithBothCurrencies(props.row.guessed_price_amount_cents,
                      props.row.source_listing_currency,
                      false) }}
                  </div> -->
                  <div class="text-weight-medium">
                    {{
                      formatPriceWithBothCurrencies(
                        props.row.guessed_price_in_ui_currency_cents,
                        props.row.ui_currency,
                        true,
                        props.row.source_listing_currency
                      )
                    }}
                  </div>
                </q-td>
              </template>

              <template v-if="isCurrentUserSession"
                        v-slot:body-cell-actual="props">
                <q-td :props="props">
                  <div v-if="props.row.source_listing_currency"
                       class="text-weight-medium">
                    {{
                      formatPriceWithBothCurrencies(
                        props.row.price_at_time_of_estimate_cents,
                        props.row.source_listing_currency,
                        false
                      )
                    }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-difference="props">
                <q-td :props="props">
                  <q-chip :color="getScoreColor(props.row.score_for_guess || 0)"
                          text-color="white"
                          dense
                          size="md">
                    {{ props.row.percentage_above_or_below > 0 ? '+' : ''
                    }}{{ props.row.percentage_above_or_below?.toFixed(1) }}%
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-score="props">
                <!-- <q-td :props="props">
                  <div class="score-cell">
                    <q-circular-progress :value="props.row.score_for_guess || 0"
                                         size="40px"
                                         :thickness="0.15"
                                         :color="getScoreColor(props.row.score_for_guess || 0)"
                                         track-color="grey-3"
                                         class="q-mr-sm">
                      <div class="text-caption text-weight-bold">{{ props.row.score_for_guess || 0 }}</div>
                    </q-circular-progress>
                  </div>
                </q-td> -->
                <q-td :props="props">
                  <div class="score-cell">
                    <q-chip :color="getScoreColor(props.row.score_for_guess || 0)"
                            text-color="white"
                            dense
                            size="md"
                            class="q-pa-sm">
                      {{ props.row.score_for_guess || 0 }}
                    </q-chip>
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>

        <!-- Comparison Summary -->
        <q-card v-if="comparisonSummary.length > 0"
                class="comparison-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 q-mb-md">
              <q-icon name="people"
                      color="primary"
                      size="sm"
                      class="q-mr-sm" />
              {{ isCurrentUserSession ? 'How You Compare to Other Players' : `How ${ssGameSession.game_player_nickname}
              Compares
              to Other Players` }}
            </div>
            <!-- The isLoadingComparisons spinner is no longer needed as it all loads at once -->
            <div>
              <div v-for="(propertyData, index) in comparisonSummary"
                   :key="index"
                   class="property-comparison q-mb-lg">
                <div class="comparison-header q-mb-md">
                  <div v-if="propertyData.property_url"
                       class="text-subtitle1 text-weight-medium">
                    <a :href="propertyData.property_url">{{
                      propertyData.property_title
                      }}</a>
                  </div>
                  <div v-else
                       class="text-subtitle1 text-weight-medium">
                    {{ propertyData.property_title }}
                  </div>
                  <div class="text-caption text-grey-6">
                    {{ propertyData.property_vicinity }}
                  </div>
                </div>

                <!-- Your guess vs others - only show prices for current user -->
                <div v-if="isCurrentUserSession"
                     class="comparison-stats q-mb-md">
                  <div class="row q-col-gutter-md">
                    <div class="col-12 col-md-4">
                      <q-card flat
                              bordered
                              class="stat-card">
                        <q-card-section class="text-center q-pa-md">
                          <div class="text-h6 text-primary">
                            {{ propertyData.your_guess_formatted }}
                          </div>
                          <div class="text-caption text-grey-6">Your Guess</div>
                        </q-card-section>
                      </q-card>
                    </div>
                    <div class="col-12 col-md-4">
                      <q-card flat
                              bordered
                              class="stat-card">
                        <q-card-section class="text-center q-pa-md">
                          <div class="text-h6 text-secondary">
                            {{ propertyData.average_guess_formatted }}
                          </div>
                          <div class="text-caption text-grey-6">
                            Average Guess
                          </div>
                        </q-card-section>
                      </q-card>
                    </div>
                    <div class="col-12 col-md-4">
                      <q-card flat
                              bordered
                              class="stat-card">
                        <q-card-section class="text-center q-pa-md">
                          <div class="text-h6 text-positive">
                            {{ propertyData.actual_price_formatted }}
                          </div>
                          <div class="text-caption text-grey-6">
                            Actual Price
                          </div>
                        </q-card-section>
                      </q-card>
                    </div>
                  </div>
                </div>

                <!-- Performance ranking -->
                <div class="performance-ranking">
                  <div class="text-subtitle2 q-mb-sm">{{ isCurrentUserSession ? 'Your Performance' :
                    `${ssGameSession.game_player_nickname}'s Performance` }}</div>
                  <div class="ranking-info">
                    <q-chip :color="propertyData.ranking.color"
                            text-color="white"
                            icon="emoji_events">
                      Ranked {{ propertyData.ranking.rank }} of
                      {{ propertyData.ranking.total_players }}
                    </q-chip>
                    <span class="q-ml-md text-body2 text-grey-7">{{
                      propertyData.ranking.performance_text
                      }}</span>
                  </div>
                </div>
                <q-separator v-if="index < comparisonSummary.length - 1"
                             class="q-mt-lg" />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- No Results -->
      <div v-else
           class="no-results-container text-center q-pa-xl">
        <q-icon name="search_off"
                color="grey-5"
                size="3em" />
        <div class="q-mt-md text-h6 text-grey-7">No Results Found</div>
        <div class="text-body2 text-grey-6 q-mb-lg">
          We couldn't find any results for this game session.
        </div>
        <q-btn color="primary"
               label="Start New Game"
               @click="startNewGame" />
      </div>
      <q-no-ssr>
        <div v-if="gameCommunitiesDetailsCalc.show"
             class="results-actions q-pa-md text-center bg-grey-2 rounded-borders shadow-2">
          <!-- <div class="q-mb-md">
            <q-icon name="fab fa-reddit-alien"
                    size="sm"
                    color="red-6"
                    class="q-mr-sm" />
            <span class="text-subtitle1 text-grey-8">Join the discussion on Reddit:</span>
            <a :href="gameCommunitiesDetailsCalc.redditCommunity.url"
               target="_blank"
               class="text-blue-7 hover:text-blue-9 q-ml-sm text-weight-medium">
              {{ gameCommunitiesDetailsCalc.redditCommunity.name || gameCommunitiesDetailsCalc.redditCommunity.url }}
            </a>
          </div>

          <div class="q-mt-lg">
            <h6 class="text-h6 text-grey-9 q-mb-sm">Other price guess games you might like:</h6>
            <div class="row justify-center q-gutter-sm">
              <div v-for="(game, index) in gameCommunitiesDetailsCalc.relevantGames"
                   :key="index"
                   class="col-auto">
                <q-chip :to="game"
                        target="_blank"
                        clickable
                        color="blue-grey-1"
                        text-color="blue-8"
                        class="hover:bg-blue-3">
                  <a :href="`https://${game}`">
                    {{ `https://${game}` }}
                  </a>
                </q-chip>
              </div>
            </div>
          </div> -->

          <!-- Action Buttons -->
          <!-- <div class="q-mt-lg row justify-center q-gutter-md">
            <q-btn color="primary"
                   label="Play Again"
                   icon="refresh"
                   size="lg"
                   rounded
                   unelevated
                   @click="playAgain" />
            <q-btn color="secondary"
                   label="Share Results"
                   icon="share"
                   size="lg"
                   rounded
                   outline
                   @click="shareResults" />
          </div> -->

          <q-card-section class="text-center">
            <div class="text-h6 q-mb-sm">🎮 Join the Community Discussion</div>
            <q-btn :label="gameCommunitiesDetailsCalc.redditCommunity.url"
                   :href="gameCommunitiesDetailsCalc.redditCommunity.url"
                   type="a"
                   color="red"
                   target="_blank"
                   icon="mdi-reddit"
                   flat />
          </q-card-section>

          <!-- <q-separator spaced /> -->

          <q-card-section>
            <div class="text-subtitle1 q-mb-sm text-center">
              🧠 Other Price Guess Games You Might Like
            </div>
            <q-list bordered
                    separator
                    class="rounded-borders">
              <q-item v-for="(
relevantGame, index
                ) in gameCommunitiesDetailsCalc.relevantGames"
                      :key="index"
                      clickable>
                <q-item-section>
                  <a :href="`https://${relevantGame}`"
                     target="_blank"
                     class="text-primary">
                    {{ `https://${relevantGame}` }}
                  </a>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </div>
      </q-no-ssr>
    </div>

    <!-- Feedback Popup Dialog -->
    <q-dialog v-model="showFeedbackDialog"
              :persistent="false"
              maximized
              transition-show="slide-up"
              transition-hide="slide-down"
              @hide="dismissFeedbackDialog">
      <q-card class="feedback-dialog-card">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">🎉 Thanks for Playing!</div>
          <q-space />
          <q-btn icon="close"
                 flat
                 round
                 dense
                 v-close-popup />
        </q-card-section>

        <q-card-section class="q-pt-none">
          <div class="text-body1 q-mb-lg">
            We'd love to hear your thoughts about the Property Price Challenge!
          </div>

          <!-- Feedback Form -->
          <q-form @submit="submitFeedback"
                  class="q-gutter-md">
            <!-- General Feedback -->
            <div>
              <q-input v-model="feedbackForm.generalFeedback"
                       type="textarea"
                       label="How was your experience? Any suggestions for improvement?"
                       outlined
                       rows="4"
                       hint="Optional - but we really appreciate your thoughts!" />
            </div>

            <!-- Create Own Game Section -->
            <q-separator class="q-my-lg" />

            <div class="create-game-section">
              <div class="text-h6 q-mb-md">
                🎮 Want to Create Your Own Game?
              </div>
              <div class="text-body2 q-mb-md text-grey-7">
                Create a custom property price guessing game for your area or
                community on propertysquares.com
              </div>

              <q-checkbox v-model="feedbackForm.wantsToCreateGame"
                          label="Yes, I'm interested in creating my own property price game!"
                          color="primary" />

              <!-- Show email/subdomain fields if interested -->
              <div v-if="feedbackForm.wantsToCreateGame"
                   class="q-mt-md q-gutter-md">
                <q-input v-model="feedbackForm.email"
                         type="email"
                         label="Your Email Address"
                         outlined
                         required
                         :rules="[
                          (val) => !!val || 'Email is required',
                          (val) =>
                            /.+@.+\..+/.test(val) || 'Please enter a valid email',
                        ]" />

                <q-input :model-value="feedbackForm.subdomain"
                         @update:model-value="
                          (val) => (feedbackForm.subdomain = val.toLowerCase())
                        "
                         label="Desired Subdomain"
                         outlined
                         required
                         prefix="https://"
                         suffix=".propertysquares.com"
                         hint="Choose a unique name for your game (e.g., 'mygame' for mygame.propertysquares.com)"
                         :rules="[
                          (val) => !!val || 'Subdomain is required',
                          (val) =>
                            /^[a-z0-9-]+$/.test(val) ||
                            'Only lowercase letters, numbers, and hyphens allowed',
                        ]" />

                <!-- <q-input v-model="feedbackForm.gameDescription"
                         type="textarea"
                         label="Describe your game idea"
                         outlined
                         rows="3"
                         hint="What area/properties would you like to feature? Any special requirements?" /> -->
              </div>
            </div>

            <!-- Submit Buttons -->
            <div class="row q-gutter-md q-mt-lg">
              <q-btn type="submit"
                     color="primary"
                     label="Submit Feedback"
                     :loading="feedbackLoading"
                     :disable="feedbackForm.wantsToCreateGame &&
                      (!feedbackForm.email || !feedbackForm.subdomain)
                      " />

              <q-btn label="Maybe Later"
                     color="grey"
                     flat
                     @click="dismissFeedbackDialog" />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
// Import the new composable
import { useServerRealtyGameResults } from '../composables/useServerRealtyGameResults'
import { useCurrencyConverter } from 'src/concerns/realty-game/composables/useCurrencyConverter'
import { useRealtyGameStorage } from 'src/concerns/realty-game/composables/useRealtyGameStorage'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
import { useRealtyGameStore } from 'src/stores/realtyGame'

const props = defineProps({
  routeSssnId: {
    type: String,
    required: true,
  },
  gameCommunitiesDetails: {
    type: Object,
  },
})

const $router = useRouter()
const $route = useRoute()
const $q = useQuasar()

// Initialize the new composable
const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
  getScoreColor,
} = useServerRealtyGameResults()

// Initialize currency converter
const { setCurrency, formatPriceWithBothCurrencies } = useCurrencyConverter()

// Initialize storage to get currency selection and current session
const { getCurrencySelection, currentSessionId } = useRealtyGameStorage()

// Check if this is the current user's session or someone else's
const isCurrentUserSession = computed(() => {
  return currentSessionId.value === props.routeSssnId
})

// Set currency from session
const sessionCurrency = getCurrencySelection(props.routeSssnId)
if (sessionCurrency) {
  setCurrency(sessionCurrency)
}

// Feedback popup state
const showFeedbackDialog = ref(false)
const feedbackLoading = ref(false)
const feedbackForm = ref({
  generalFeedback: '',
  wantsToCreateGame: false,
  email: '',
  subdomain: '',
  gameDescription: '',
})

// LocalStorage keys for feedback popup dismissal and delay
const FEEDBACK_DISMISSED_KEY = 'realty_game_feedback_dismissed'
const FEEDBACK_DISMISS_COUNT_KEY = 'realty_game_feedback_dismiss_count'

// Add computed for leaderboard and overall ranking
const leaderboard = computed(() => results.value?.leaderboard || [])
const overallRanking = computed(() => results.value?.overall_ranking || null)

// Only show leaderboard if ?showLb is present in the query
const showLeaderboard = computed(() => !!$route.query.showLb)

// Leaderboard columns
const leaderboardColumns = [
  {
    name: 'session_guest_name',
    label: 'Player',
    field: 'session_guest_name',
    align: 'left',
  },
  { name: 'total_score', label: 'Score', field: 'total_score', align: 'right' },
  {
    name: 'max_possible_score',
    label: 'Max Score',
    field: 'max_possible_score',
    align: 'right',
  },
  {
    name: 'performance_percentage',
    label: '%',
    field: 'performance_percentage',
    align: 'right',
  },
  { name: 'created_at', label: 'Date', field: 'created_at', align: 'right' },
]

// Computed properties for the component
const gameCommunitiesDetailsCalc = computed(() => {
  // This logic is client-specific and remains here
  if (typeof window !== 'undefined') {
    // const currentHost = location.host;
    // const hosts = ['nuneaton.propertysquares.com', 'brum.propertysquares.com', 'brum-houses.propertysquares.com'];
    // const relevantGames = hosts.filter(host => host !== currentHost);
    // let redC = {
    //   url: 'https://www.reddit.com/r/propertysquares/',
    //   text: 'Join the discussion on reddit!'
    // };
    // if (currentHost === 'nuneaton.propertysquares.com') {
    //   redC.url = "https://www.reddit.com/r/nuneaton/";
    // }
    let showComDetails = false
    if (props.gameCommunitiesDetails.redditCommunity) {
      showComDetails = true
    }
    if (!props.gameCommunitiesDetails.show) {
      // if the show is false from server, then we don't want to show the details
      showComDetails = false
    }
    return {
      show: showComDetails,
      redditCommunity: props.gameCommunitiesDetails.redditCommunity,
      relevantGames: props.gameCommunitiesDetails.relevantGames,
    }
  } else {
    return { show: false }
  }
})

const resultsColumns = computed(() => {
  const columns = [
    {
      name: 'property',
      label: 'Property',
      field: 'property',
      align: 'left',
      style: 'width: 30%',
    },
  ]

  // Only show price columns for current user's session
  if (isCurrentUserSession.value) {
    columns.push(
      {
        name: 'guess',
        label: 'Your Guess',
        field: 'guess',
        align: 'right',
        style: 'width: 20%',
      },
      {
        name: 'actual',
        label: 'Actual Price',
        field: 'actual',
        align: 'right',
        style: 'width: 20%',
      }
    )
  }

  // Always show difference and score
  columns.push(
    {
      name: 'difference',
      label: 'Difference',
      field: 'difference',
      align: 'center',
      style: isCurrentUserSession.value ? 'width: 15%' : 'width: 35%',
    },
    {
      name: 'score',
      label: 'Score',
      field: 'score',
      align: 'center',
      style: isCurrentUserSession.value ? 'width: 15%' : 'width: 35%',
    }
  )

  return columns
})

// Methods
const loadResults = async () => {
  await fetchResults(
    props.routeSssnId,
    $router.currentRoute.value.params.gameSlug
  )
}

const startNewGame = () => {
  $router.push({ name: 'rPriceGuessStart' })
}

// Helper to get exponential delay (base: 35s, double each time, max 24h)
function getFeedbackDelay() {
  let count = 0
  if (typeof window !== 'undefined') {
    count = parseInt(
      localStorage.getItem(FEEDBACK_DISMISS_COUNT_KEY) || '0',
      10
    )
  }
  // base delay: 35s, double each time, max 24h
  const base = 35000
  const max = 24 * 60 * 60 * 1000 // 24 hours
  return Math.min(base * Math.pow(2, count), max)
}

// Feedback popup methods
const showFeedbackPopup = () => {
  // Check if feedback has been dismissed before
  if (typeof window !== 'undefined') {
    const dismissed = localStorage.getItem(FEEDBACK_DISMISSED_KEY)
    if (!dismissed) {
      showFeedbackDialog.value = true
    }
  }
}

const dismissFeedbackDialog = () => {
  showFeedbackDialog.value = false
  // Mark as dismissed in localStorage and increment dismiss count
  if (typeof window !== 'undefined') {
    localStorage.setItem(FEEDBACK_DISMISSED_KEY, 'true')
    let count = parseInt(
      localStorage.getItem(FEEDBACK_DISMISS_COUNT_KEY) || '0',
      10
    )
    localStorage.setItem(FEEDBACK_DISMISS_COUNT_KEY, String(count + 1))
  }
}

const submitFeedback = async () => {
  feedbackLoading.value = true

  try {
    const feedbackData = {
      game_session_id: props.routeSssnId,
      general_feedback: feedbackForm.value.generalFeedback,
      wants_to_create_game: feedbackForm.value.wantsToCreateGame,
      email: feedbackForm.value.email,
      subdomain: feedbackForm.value.subdomain,
      game_description: feedbackForm.value.gameDescription,
      submitted_at: new Date().toISOString(),
      user_agent: navigator.userAgent,
      page_url: window.location.href,
    }

    // Submit to backend API
    const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/forms_ppsq/sp/price_game_followup`
    const response = await axios.post(apiUrl, {
      feedback: feedbackData,
    })

    console.log('Feedback submitted successfully:', response.data)

    // Show success message
    $q.notify({
      message: feedbackForm.value.wantsToCreateGame
        ? "Thank you! We'll contact you about creating your custom game."
        : 'Thank you for your feedback!',
      color: 'positive',
      icon: 'check_circle',
      position: 'top',
    })

    // Mark as dismissed and close dialog
    dismissFeedbackDialog()

    // Reset form
    feedbackForm.value = {
      generalFeedback: '',
      wantsToCreateGame: false,
      email: '',
      subdomain: '',
      gameDescription: '',
    }
  } catch (error) {
    console.error('Failed to submit feedback:', error)

    let errorMessage = 'Failed to submit feedback. Please try again.'

    // Provide more specific error messages
    if (error.response?.status === 404) {
      errorMessage =
        'Feedback service is temporarily unavailable. Please try again later.'
    } else if (error.response?.status === 422) {
      errorMessage = 'Please check your input and try again.'
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (!navigator.onLine) {
      errorMessage = 'Please check your internet connection and try again.'
    }

    $q.notify({
      message: errorMessage,
      color: 'negative',
      icon: 'error',
      position: 'top',
    })
  } finally {
    feedbackLoading.value = false
  }
}

// Timer for feedback popup
let feedbackTimer = null

// Initialize on mount
onMounted(() => {
  loadResults()
  // Set timer to show feedback popup after exponential delay
  let delay = getFeedbackDelay()
  feedbackTimer = setTimeout(() => {
    showFeedbackPopup()
  }, delay)
})

// Cleanup timer on unmount
onUnmounted(() => {
  if (feedbackTimer) {
    clearTimeout(feedbackTimer)
  }
})
</script>

<script>
// useRealtyGameMetaStore and useRealtyGameStore are already imported in <script setup>, so do not import again
// axios and pwbFlexConfig are already imported in <script setup>, so do not import again

// Helper function to fetch and process data, used by both preFetch and client-side logic
const fetchAndSetGameData = async (gameSlug, store) => {
  const response = await axios.get(
    `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/${gameSlug}`
  )

  if (response.data) {
    const gameData = response.data.price_guess_inputs
    const realtyGameDetails = response.data.realty_game_details
    const gameListings =
      gameData?.game_listings?.filter(
        (game) => game.listing_details.visible === true
      ) || []

    const storeData = {
      gameListings: gameListings,
      gameTitle: realtyGameDetails?.game_title || 'Property Price Challenge',
      gameDesc: realtyGameDetails?.game_description || '',
      gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
      gameDefaultCurrency: gameData?.default_currency || 'GBP',
      totalProperties: gameListings.length,
      currentProperty: null,
      isDataLoaded: true, // Flag to indicate data is ready
    }

    store.setRealtyGameData(storeData)
    return storeData
  }
  return null
}

export default {
  async preFetch({ currentRoute, ssrContext }) {
    const gameSlug = currentRoute.params.gameSlug
    if (!gameSlug) return

    try {
      const store = useRealtyGameStore()
      const metaStore = useRealtyGameMetaStore()
      const gameData = await fetchAndSetGameData(gameSlug, store)
      // Set meta tags in the meta store
      console.log(`setting meta tags with ${gameData.gameTitle}`)
      // would be nice to have the game_player_nickname in the meta tags
      // be this is not available in prefetch ${ssGameSession.game_player_nickname}
      // could retrieve player_results.session_guest_name from the server
      // but the juice is not worth the squeeze...
      if (gameData) {
        metaStore.setMeta({
          title: `Property Price Challenge results for: ${gameData.gameTitle}`,
          description:
            gameData.gameDesc ||
            'See your results and compare your property price guessing skills with others!',
          image:
            gameData.gameBgImageUrl ||
            'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
          url: `https://yourdomain.com${currentRoute.fullPath}`,
          keywords: `property price game, real estate challenge, property valuation, house price quiz, market knowledge test, ${gameData.gameTitle || ''
            }`,
        })
      }
      if (ssrContext) {
        ssrContext.realtyGameData = gameData
      }
    } catch (error) {
      console.error('preFetch error for results page:', error)
      // fail silently for meta
    }
  },
}
</script>

<style scoped>
.realty-game-results-page {
  /* background-color: #fafafa; */
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container,
.no-results-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-badge {
  display: inline-block;
  padding: 2rem;
  /* background: #f8f9fa; */
  border-radius: 16px;
  border: 2px solid #e9ecef;
}

.session-info {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.results-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.results-table {
  border-radius: 8px;
}

/*
.property-cell {
  max-width: 200px;
}
*/

@media (max-width: 768px) {
  .property-cell {
    max-width: 200px;
  }
}

.score-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/*
.property-comparison {
  border-left: 4px solid #e0e0e0;
  padding-left: 1rem;
}
  */

.comparison-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.stat-card {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.performance-ranking {
  /* background: #f0f4f8; */
  border-radius: 8px;
  padding: 1rem;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.results-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Button improvements */
.q-btn {
  text-transform: none;
  font-weight: 500;
}

/* Feedback Dialog Styles */
.feedback-dialog-card {
  max-width: 600px;
  margin: 0 auto;
  max-height: 90vh;
  overflow-y: auto;
}

.create-game-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #1976d2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* .max-ctr {
    padding: 1rem;
  } */

  .results-header {
    padding: 1rem;
  }

  .session-info {
    flex-direction: column;
    align-items: center;
  }

  .comparison-stats .row {
    flex-direction: column;
  }

  .ranking-info {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
