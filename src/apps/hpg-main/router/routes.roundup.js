export default [
  // {
  //   path: 'roundup',
  //   name: 'rRoundupsLanding',
  //   component: () => import('src/concerns/hpg/pages/HpgHomePage2025.vue'),
  // },
  // {
  //   path: 'creat-one-off-game',
  //   name: 'rCreateOneOffGame',
  //   component: () => import('src/concerns/hpg/pages/HpgHomePage2025.vue'),
  // },
  {
    path: 'realty-game-session',
    name: 'rRealtyGameSession',
    // path: 'price-guess-session',
    // name: 'rPriceGuessSession',
    component: () =>
      import('src/concerns/realty-game/pages/RealtyGameSession.vue'),
    // import('src/concerns/price-guess/pages/PriceGuessSession.vue'),
    // meta: {
    //   title: 'Property Price Game Session - Active Game',
    //   description:
    //     'Active property price guessing game session. Test your knowledge of local property values.',
    //   keywords: 'property game, price guessing, real estate quiz',
    //   ogType: 'website',
    //   robots: 'noindex, nofollow', // Game session
    // },
  },
  {
    path: 'roundup/v/:gameSlug',
    name: 'rRoundupGame',
    component: () =>
      import('src/concerns/realty-game/layouts/RoundupGameLayout.vue'),
    meta: {
      title: 'Property Price Challenge - Interactive Real Estate Game',
      description:
        'Test your property market knowledge with our interactive price guessing game. Challenge yourself with real properties and see how well you know local values.',
      keywords:
        'property price game, real estate challenge, property valuation, house price quiz, market knowledge test',
      ogType: 'website',
      ogImage:
        'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
      twitterCard: 'summary_large_image',
    },
    children: [
      {
        path: '',
        name: 'rRoundupGameStart',
        component: () =>
          import(
            'src/concerns/realty-game/pages/roundup/RoundupGameStartPage.vue'
          ),
        meta: {
          title: 'Start Property Price Challenge - Test Your Market Knowledge',
          description:
            'Ready to test your property market knowledge? Start our interactive price guessing game and see how well you know local property values.',
          keywords:
            'start property game, price challenge, real estate quiz, property knowledge test',
          ogType: 'website',
        },
      },
      {
        path: 'superbee',
        name: 'rRoundupGamePropertiesAdmin',
        // might do - might eventually need a version of 
        // 
        component: () =>
          import(
            'src/concerns/realty-game/pages/one-off/OneOffGamePropertiesAdmin.vue'
          ),
      },
      {
        path: 'property/:listingInGameUuid',
        component: () =>
          import('src/concerns/realty-game/layouts/RoundupGamePagesLayout.vue'),
        children: [
          {
            path: '',
            name: 'rRoundupGameProperty',
            component: () =>
              import(
                'src/concerns/realty-game/pages/roundup/RoundupGamePropertyPage.vue'
              ),
            props: (route) => ({
              listingInGameUuid: route.params.listingInGameUuid,
              routeSssnId: route.query.session,
            }),
            meta: {
              title: 'Guess Property Price - Property Price Challenge',
              description:
                "Make your best guess at this property's value. Test your knowledge of the local property market.",
              keywords:
                'property price guess, house valuation, property game, real estate quiz',
              ogType: 'website',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
          {
            path: 'superbee',
            name: 'rRoundupGamePropertySuperbee',
            component: () =>
              import(
                'src/concerns/realty-game/pages/roundup/RoundupGamePropertyEditPage.vue'
              ),
            props: (route) => ({
              listingInGameUuid: route.params.listingInGameUuid,
              routeSssnId: route.query.session,
            }),
            meta: {
              title: 'Edit Property in Game - Property Price Challenge',
              description:
                'Edit property attributes for this game listing, including visibility, position, and images.',
              keywords:
                'edit property, game property admin, property attributes, property images',
              ogType: 'website',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
        ],
      },
      {
        path: 'results/p/:listingInGameUuid/:routeSssnId',
        component: () =>
          import('src/concerns/realty-game/layouts/RoundupGamePagesLayout.vue'),
        children: [
          {
            path: '',
            name: 'rRoundupGameResultsSummary',
            component: () =>
              import(
                'src/concerns/realty-game/pages/roundup/RoundupGameResultsSummaryPage.vue'
              ),
            props: true,
            meta: {
              title: 'Your Property Price Challenge Results',
              description:
                'See how well you performed in the property price challenge. Share your results with friends!',
              keywords:
                'property game results, price guess results, property knowledge score',
              ogType: 'website',
              ogImage:
                'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
        ],
      },
      {
        path: 'results_to_share/p/:listingInGameUuid/:routeSssnId',
        component: () =>
          import('src/concerns/realty-game/layouts/RoundupGamePagesLayout.vue'),
        children: [
          {
            path: '',
            name: 'rRoundupGameResultsShareable',
            component: () =>
              import(
                'src/concerns/realty-game/pages/roundup/RoundupGameResultsShareablePage.vue'
              ),
            props: true,
            meta: {
              title: 'Property Price Challenge Results - Shareable',
              description:
                'Property price challenge results without revealing actual prices. Perfect for sharing with friends!',
              keywords:
                'property game results, price guess results, shareable results, property knowledge',
              ogType: 'website',
              ogImage:
                'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
        ],
      },
      {
        path: 'detailed_results/p/:listingInGameUuid/:routeSssnId',
        component: () =>
          import('src/concerns/realty-game/layouts/RoundupGamePagesLayout.vue'),
        children: [
          {
            path: '',
            name: 'rRoundupGameResultsDetailed',
            component: () =>
              import(
                'src/concerns/realty-game/pages/roundup/RoundupGameResultsDetailedPage.vue'
              ),
            props: true,
            meta: {
              title: 'Property Price Challenge - Detailed Results',
              description:
                'Complete property price challenge results with all prices and detailed breakdown.',
              keywords:
                'property game results, price guess results, detailed results, property prices',
              ogType: 'website',
              ogImage:
                'https://assets.propertysquares.com/staticfiles/ppsqs/house_price_guess_on_map_concept.jpeg',
              robots: 'noindex, nofollow', // Game session specific
            },
          },
        ],
      },
    ],
  },
]
